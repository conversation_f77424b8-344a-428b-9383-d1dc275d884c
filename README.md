# TypeScript Node.js Express Server

A modern TypeScript-based Node.js web server using Express framework with best practices and security middleware.

## Features

- 🚀 **TypeScript** - Full TypeScript support with strict type checking
- ⚡ **Express.js** - Fast, unopinionated web framework
- 🔒 **Security** - Helmet.js for security headers
- 🌐 **CORS** - Cross-Origin Resource Sharing support
- 📝 **Logging** - Morgan HTTP request logger
- 🔄 **Hot Reload** - Nodemon for development
- 📦 **pnpm** - Fast, disk space efficient package manager
- 🎯 **Modern Node.js** - Requires Node.js 22+

## Prerequisites

- Node.js 22 or higher
- pnpm package manager

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd wsnode-server
```

2. Install dependencies:
```bash
pnpm install
```

## Development

Start the development server with hot reload:
```bash
pnpm dev
```

The server will start on `http://localhost:3000`

## Production

1. Build the project:
```bash
pnpm build
```

2. Start the production server:
```bash
pnpm start
```

## Available Scripts

- `pnpm dev` - Start development server with hot reload
- `pnpm build` - Build the project for production
- `pnpm start` - Start the production server
- `pnpm clean` - Clean the dist directory
- `pnpm typecheck` - Run TypeScript type checking

## API Endpoints

### Health Check
- `GET /health` - Server health status

### Root
- `GET /` - Welcome message and available endpoints

### API Routes
- `GET /api` - API information
- `GET /api/users` - Get all users (example)
- `POST /api/users` - Create a new user (example)

## Project Structure

```
src/
├── app.ts              # Express application setup
├── server.ts           # Server entry point
├── routes/             # Route handlers
│   └── index.ts        # Main API routes
├── middleware/         # Custom middleware (empty)
└── types/              # TypeScript type definitions (empty)
```

## Environment Variables

Create a `.env` file in the root directory:

```env
PORT=3000
NODE_ENV=development
```

## Security Features

- **Helmet.js** - Sets various HTTP headers for security
- **CORS** - Configurable Cross-Origin Resource Sharing
- **Request size limits** - Body parser with size limits
- **Error handling** - Global error handler with environment-aware responses

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

ISC
