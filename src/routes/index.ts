import { Router, Request, Response } from 'express';

const router: Router = Router();

// GET /api
router.get('/', (req: Request, res: Response) => {
  res.json({
    message: 'API is working!',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    endpoints: [
      'GET /api - This endpoint',
      'GET /api/users - Get all users (example)',
      'POST /api/users - Create a user (example)'
    ]
  });
});

// Example users endpoint
router.get('/users', (req: Request, res: Response) => {
  // This is a mock endpoint - replace with actual database logic
  const users = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>' },
    { id: 3, name: '<PERSON>', email: '<EMAIL>' }
  ];

  res.json({
    success: true,
    data: users,
    count: users.length
  });
});

// Example POST endpoint
router.post('/users', (req: Request, res: Response): void => {
  const { name, email } = req.body;

  // Basic validation
  if (!name || !email) {
    res.status(400).json({
      success: false,
      error: 'Name and email are required'
    });
    return;
  }

  // Mock user creation - replace with actual database logic
  const newUser = {
    id: Date.now(), // Simple ID generation for demo
    name,
    email,
    createdAt: new Date().toISOString()
  };

  res.status(201).json({
    success: true,
    message: 'User created successfully',
    data: newUser
  });
});

export default router;
