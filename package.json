{"name": "wsnode-server", "version": "1.0.0", "description": "TypeScript Node.js Express server", "main": "dist/server.js", "type": "commonjs", "engines": {"node": ">=22.0.0"}, "scripts": {"build": "rimraf dist && tsc", "start": "node dist/server.js", "dev": "nodemon src/server.ts", "test": "echo \"Error: no test specified\" && exit 1", "clean": "<PERSON><PERSON><PERSON> dist", "typecheck": "tsc --noEmit"}, "keywords": ["typescript", "node", "express", "server"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "express": "^4.18.0", "helmet": "^8.1.0", "morgan": "^1.10.0"}, "devDependencies": {"@types/cors": "^2.8.18", "@types/express": "^4.17.0", "@types/morgan": "^1.9.9", "@types/node": "^22.15.29", "nodemon": "^3.1.10", "rimraf": "^6.0.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}