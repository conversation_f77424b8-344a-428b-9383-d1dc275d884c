hoistPattern:
  - '*'
hoistedDependencies:
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/body-parser@1.19.5':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/express-serve-static-core@4.19.6':
    '@types/express-serve-static-core': private
  '@types/http-errors@2.0.4':
    '@types/http-errors': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/send@0.17.4':
    '@types/send': private
  '@types/serve-static@1.15.7':
    '@types/serve-static': private
  accepts@1.3.8:
    accepts: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.14.1:
    acorn: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  arg@4.1.3:
    arg: private
  array-flatten@1.1.1:
    array-flatten: private
  balanced-match@1.0.2:
    balanced-match: private
  basic-auth@2.0.1:
    basic-auth: private
  binary-extensions@2.3.0:
    binary-extensions: private
  body-parser@1.20.3:
    body-parser: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bound@1.0.4:
    call-bound: private
  chokidar@3.6.0:
    chokidar: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  concat-map@0.0.1:
    concat-map: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  debug@2.6.9:
    debug: private
  depd@2.0.0:
    depd: private
  destroy@1.2.0:
    destroy: private
  diff@4.0.2:
    diff: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  emoji-regex@8.0.0:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  escape-html@1.0.3:
    escape-html: private
  etag@1.8.1:
    etag: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  foreground-child@3.3.1:
    foreground-child: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@5.1.2:
    glob-parent: private
  glob@11.0.2:
    glob: private
  gopd@1.2.0:
    gopd: private
  has-flag@3.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  hasown@2.0.2:
    hasown: private
  http-errors@2.0.0:
    http-errors: private
  iconv-lite@0.4.24:
    iconv-lite: private
  ignore-by-default@1.0.1:
    ignore-by-default: private
  inherits@2.0.4:
    inherits: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-number@7.0.0:
    is-number: private
  isexe@2.0.0:
    isexe: private
  jackspeak@4.1.1:
    jackspeak: private
  lru-cache@11.1.0:
    lru-cache: private
  make-error@1.3.6:
    make-error: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  media-typer@0.3.0:
    media-typer: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  methods@1.1.2:
    methods: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  minimatch@3.1.2:
    minimatch: private
  minipass@7.1.2:
    minipass: private
  ms@2.0.0:
    ms: private
  negotiator@0.6.3:
    negotiator: private
  normalize-path@3.0.0:
    normalize-path: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  on-finished@2.4.1:
    on-finished: private
  on-headers@1.0.2:
    on-headers: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  parseurl@1.3.3:
    parseurl: private
  path-key@3.1.1:
    path-key: private
  path-scurry@2.0.0:
    path-scurry: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  picomatch@2.3.1:
    picomatch: private
  proxy-addr@2.0.7:
    proxy-addr: private
  pstree.remy@1.1.8:
    pstree.remy: private
  qs@6.13.0:
    qs: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@2.5.2:
    raw-body: private
  readdirp@3.6.0:
    readdirp: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safer-buffer@2.1.2:
    safer-buffer: private
  semver@7.7.2:
    semver: private
  send@0.19.0:
    send: private
  serve-static@1.16.2:
    serve-static: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-update-notifier@2.0.0:
    simple-update-notifier: private
  statuses@2.0.1:
    statuses: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  strip-ansi@6.0.1:
    strip-ansi-cjs: private
  strip-ansi@7.1.0:
    strip-ansi: private
  supports-color@5.5.0:
    supports-color: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toidentifier@1.0.1:
    toidentifier: private
  touch@3.1.1:
    touch: private
  type-is@1.6.18:
    type-is: private
  undefsafe@2.0.5:
    undefsafe: private
  undici-types@6.21.0:
    undici-types: private
  unpipe@1.0.0:
    unpipe: private
  utils-merge@1.0.1:
    utils-merge: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  vary@1.1.2:
    vary: private
  which@2.0.2:
    which: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  yn@3.1.1:
    yn: private
ignoredBuilds: []
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.2.1
pendingBuilds: []
prunedAt: Mon, 02 Jun 2025 22:26:03 GMT
publicHoistPattern: []
registries:
  default: https://registry.npmjs.org/
skipped: []
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
