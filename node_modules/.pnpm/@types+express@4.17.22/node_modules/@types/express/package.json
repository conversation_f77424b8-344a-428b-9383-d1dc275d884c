{"name": "@types/express", "version": "4.17.22", "description": "TypeScript definitions for express", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/express", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>et A<PERSON>ra", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/puneetar"}, {"name": "<PERSON>", "githubUsername": "dfrankland", "url": "https://github.com/dfrankland"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/express"}, "scripts": {}, "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "0419587ffe8374d5cf260318c152eae67d48eb163a0fe4677e05b55886ea22d6", "typeScriptVersion": "5.1"}