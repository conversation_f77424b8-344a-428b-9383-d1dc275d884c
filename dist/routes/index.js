"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const router = (0, express_1.Router)();
router.get('/', (req, res) => {
    res.json({
        message: 'API is working!',
        version: '1.0.0',
        timestamp: new Date().toISOString(),
        endpoints: [
            'GET /api - This endpoint',
            'GET /api/users - Get all users (example)',
            'POST /api/users - Create a user (example)'
        ]
    });
});
router.get('/users', (req, res) => {
    const users = [
        { id: 1, name: '<PERSON>', email: '<EMAIL>' },
        { id: 2, name: '<PERSON>', email: '<EMAIL>' },
        { id: 3, name: '<PERSON>', email: '<EMAIL>' }
    ];
    res.json({
        success: true,
        data: users,
        count: users.length
    });
});
router.post('/users', (req, res) => {
    const { name, email } = req.body;
    if (!name || !email) {
        res.status(400).json({
            success: false,
            error: 'Name and email are required'
        });
        return;
    }
    const newUser = {
        id: Date.now(),
        name,
        email,
        createdAt: new Date().toISOString()
    };
    res.status(201).json({
        success: true,
        message: 'User created successfully',
        data: newUser
    });
});
exports.default = router;
//# sourceMappingURL=index.js.map